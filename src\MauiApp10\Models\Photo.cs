using System.Text.Json.Serialization;

namespace MauiApp10.Models;

/// <summary>
/// Represents a photo with metadata stored in the database
/// </summary>
public class Photo
{
    /// <summary>
    /// Unique identifier for the photo
    /// </summary>
    public int ID { get; set; }

    /// <summary>
    /// Display name/title for the photo
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Optional description or notes about the photo
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Path to the encrypted photo file on disk
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// Path to the encrypted thumbnail file on disk
    /// </summary>
    public string ThumbnailPath { get; set; } = string.Empty;

    /// <summary>
    /// Original file name when the photo was captured
    /// </summary>
    public string OriginalFileName { get; set; } = string.Empty;

    /// <summary>
    /// File size in bytes of the original photo
    /// </summary>
    public long FileSizeBytes { get; set; }

    /// <summary>
    /// Width of the photo in pixels
    /// </summary>
    public int Width { get; set; }

    /// <summary>
    /// Height of the photo in pixels
    /// </summary>
    public int Height { get; set; }

    /// <summary>
    /// Date and time when the photo was captured
    /// </summary>
    public DateTime CapturedAt { get; set; }

    /// <summary>
    /// Date and time when the photo was added to the app
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Date and time when the photo metadata was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// GPS latitude where the photo was taken (optional)
    /// </summary>
    public double? Latitude { get; set; }

    /// <summary>
    /// GPS longitude where the photo was taken (optional)
    /// </summary>
    public double? Longitude { get; set; }

    /// <summary>
    /// MIME type of the photo (e.g., image/jpeg, image/png)
    /// </summary>
    public string MimeType { get; set; } = "image/jpeg";

    /// <summary>
    /// Whether the photo has been marked as a favorite
    /// </summary>
    public bool IsFavorite { get; set; }

    /// <summary>
    /// Tags associated with this photo (for future extensibility)
    /// </summary>
    [JsonIgnore]
    public List<Tag> Tags { get; set; } = [];

    /// <summary>
    /// Computed property for display purposes
    /// </summary>
    [JsonIgnore]
    public string DisplayTitle => !string.IsNullOrEmpty(Title) ? Title : $"Photo {ID}";

    /// <summary>
    /// Computed property for file size display
    /// </summary>
    [JsonIgnore]
    public string FileSizeDisplay
    {
        get
        {
            if (FileSizeBytes < 1024)
                return $"{FileSizeBytes} B";
            if (FileSizeBytes < 1024 * 1024)
                return $"{FileSizeBytes / 1024:F1} KB";
            if (FileSizeBytes < 1024 * 1024 * 1024)
                return $"{FileSizeBytes / (1024 * 1024):F1} MB";
            return $"{FileSizeBytes / (1024 * 1024 * 1024):F1} GB";
        }
    }

    /// <summary>
    /// Computed property for dimensions display
    /// </summary>
    [JsonIgnore]
    public string DimensionsDisplay => $"{Width} × {Height}";

    /// <summary>
    /// Computed property to check if location data is available
    /// </summary>
    [JsonIgnore]
    public bool HasLocation => Latitude.HasValue && Longitude.HasValue;

    public override string ToString() => DisplayTitle;
}
