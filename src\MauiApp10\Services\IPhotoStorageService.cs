using MauiApp10.Models;

namespace MauiApp10.Services;

/// <summary>
/// Service for secure photo storage operations
/// </summary>
public interface IPhotoStorageService
{
    /// <summary>
    /// Stores a photo securely with encryption and generates a thumbnail
    /// </summary>
    /// <param name="photo">Photo object with metadata</param>
    /// <param name="imageData">Raw image data</param>
    /// <returns>Updated photo object with file paths, or null if storage failed</returns>
    Task<Photo?> StorePhotoAsync(Photo photo, byte[] imageData);

    /// <summary>
    /// Retrieves a photo's image data by decrypting the stored file
    /// </summary>
    /// <param name="photo">Photo object with file path</param>
    /// <returns>Decrypted image data, or null if retrieval failed</returns>
    Task<byte[]?> GetPhotoDataAsync(Photo photo);

    /// <summary>
    /// Retrieves a photo's thumbnail data by decrypting the stored thumbnail file
    /// </summary>
    /// <param name="photo">Photo object with thumbnail path</param>
    /// <returns>Decrypted thumbnail data, or null if retrieval failed</returns>
    Task<byte[]?> GetThumbnailDataAsync(Photo photo);

    /// <summary>
    /// Deletes a photo's files from storage
    /// </summary>
    /// <param name="photo">Photo object with file paths</param>
    /// <returns>True if deletion was successful, false otherwise</returns>
    Task<bool> DeletePhotoFilesAsync(Photo photo);

    /// <summary>
    /// Generates a thumbnail from image data
    /// </summary>
    /// <param name="imageData">Original image data</param>
    /// <param name="maxWidth">Maximum width for thumbnail</param>
    /// <param name="maxHeight">Maximum height for thumbnail</param>
    /// <returns>Thumbnail image data</returns>
    Task<byte[]> GenerateThumbnailAsync(byte[] imageData, int maxWidth = 200, int maxHeight = 200);

    /// <summary>
    /// Gets the total storage space used by photos
    /// </summary>
    /// <returns>Total bytes used by photo storage</returns>
    Task<long> GetTotalStorageUsedAsync();

    /// <summary>
    /// Cleans up orphaned files that don't have corresponding database entries
    /// </summary>
    /// <returns>Number of files cleaned up</returns>
    Task<int> CleanupOrphanedFilesAsync();
}
