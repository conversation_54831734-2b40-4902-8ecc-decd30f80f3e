namespace MauiApp10.Data
{
    public static class Constants
    {
        public const string DatabaseFilename = "AppSQLite.db3";
        public const string PhotosDirectoryName = "Photos";
        public const string ThumbnailsDirectoryName = "Thumbnails";

        public static string DatabasePath =>
            $"Data Source={Path.Combine(FileSystem.AppDataDirectory, DatabaseFilename)}";

        public static string EncryptedDatabasePath
        {
            get
            {
                // For now, use regular SQLite until SQLCipher package is available
                // TODO: Implement SQLCipher when package is available
                return DatabasePath;
            }
        }

        public static string PhotosDirectory =>
            Path.Combine(FileSystem.AppDataDirectory, PhotosDirectoryName);

        public static string ThumbnailsDirectory =>
            Path.Combine(FileSystem.AppDataDirectory, ThumbnailsDirectoryName);

        private static string GetOrCreateDatabasePassword()
        {
            var password = Preferences.Default.Get("db_password", string.Empty);

            if (string.IsNullOrEmpty(password))
            {
                // Generate a secure random password
                var passwordBytes = new byte[32];
                using var rng = System.Security.Cryptography.RandomNumberGenerator.Create();
                rng.GetBytes(passwordBytes);
                password = Convert.ToBase64String(passwordBytes);

                Preferences.Default.Set("db_password", password);
            }

            return password;
        }

        public static void EnsureDirectoriesExist()
        {
            if (!Directory.Exists(PhotosDirectory))
                Directory.CreateDirectory(PhotosDirectory);

            if (!Directory.Exists(ThumbnailsDirectory))
                Directory.CreateDirectory(ThumbnailsDirectory);
        }
    }
}