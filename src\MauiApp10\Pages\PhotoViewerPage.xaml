<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="MauiApp10.Pages.PhotoViewerPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:pagemodels="clr-namespace:MauiApp10.PageModels"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             x:DataType="pagemodels:PhotoViewerPageModel"
             Title="{Binding Photo.DisplayTitle}"
             x:Name="PhotoViewerContentPage"
             Shell.BackgroundColor="Black">

    <ContentPage.Behaviors>
        <toolkit:EventToCommandBehavior
            BindingContext="{Binding Path=BindingContext, Source={x:Reference PhotoViewerContentPage}, x:DataType=ContentPage}"
            EventName="Appearing"
            Command="{Binding AppearingCommand}"/>
    </ContentPage.Behaviors>

    <Grid BackgroundColor="Black">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 
            Photo display area
            * Removed missing property ZoomMode: ZoomMode="Both"
        -->
        <ScrollView Grid.Row="0"
                    BackgroundColor="Black">
            <Image Source="{Binding PhotoImageSource}"
                   Aspect="AspectFit"
                   BackgroundColor="Black"/>
        </ScrollView>

        <!-- Loading indicator -->
        <ActivityIndicator Grid.Row="0"
                           IsRunning="{Binding IsBusy}"
                           IsVisible="{Binding IsBusy}"
                           Color="White"
                           WidthRequest="50"
                           HeightRequest="50"/>

        <!-- Photo info panel -->
        <Border Grid.Row="1"
                BackgroundColor="#CC000000"
                Padding="16,12"
                IsVisible="{Binding ShowInfo}">
            <StackLayout Spacing="8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Label Grid.Column="0"
                           Text="{Binding Photo.DisplayTitle}"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="White"/>

                    <Button Grid.Column="1"
                            Text="ℹ️"
                            FontSize="16"
                            BackgroundColor="Transparent"
                            TextColor="White"
                            Command="{Binding ToggleInfoCommand}"/>
                </Grid>

                <Label Text="{Binding Photo.Description}"
                       FontSize="14"
                       TextColor="White"
                       IsVisible="{Binding Photo.Description, Converter={StaticResource StringToBoolConverter}}"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Label Grid.Column="0"
                           Text="{Binding Photo.CapturedAt, StringFormat='Captured: {0:MMM dd, yyyy HH:mm}'}"
                           FontSize="12"
                           TextColor="LightGray"/>

                    <Label Grid.Column="1"
                           Text="{Binding Photo.DimensionsDisplay, StringFormat='Size: {0}'}"
                           FontSize="12"
                           TextColor="LightGray"
                           HorizontalOptions="End"/>
                </Grid>

                <Label Text="{Binding Photo.FileSizeDisplay, StringFormat='File size: {0}'}"
                       FontSize="12"
                       TextColor="LightGray"
                       IsVisible="{Binding ShowDetailedInfo}"/>

                <Label Text="📍 Location available"
                       FontSize="12"
                       TextColor="LightGray"
                       IsVisible="{Binding Photo.HasLocation}"/>
            </StackLayout>
        </Border>
    </Grid>
</ContentPage>
