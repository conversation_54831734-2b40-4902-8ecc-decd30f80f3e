<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:sf="clr-namespace:Syncfusion.Maui.Toolkit.TextInputLayout;assembly=Syncfusion.Maui.Toolkit"
             xmlns:pageModel="clr-namespace:MauiApp10.PageModels"
             xmlns:models="clr-namespace:MauiApp10.Models"
             x:DataType="pageModel:TaskDetailPageModel"
             x:Class="MauiApp10.Pages.TaskDetailPage"
             Title="Task">

    <ContentPage.ToolbarItems>
        <ToolbarItem
            Text="Delete"
            Command="{Binding DeleteCommand}"
            Order="Primary"
            Priority="0"
            IconImageSource="{StaticResource IconDelete}"
            SemanticProperties.Description="Delete" />
    </ContentPage.ToolbarItems>        

    <Grid>
        <ScrollView>
            <VerticalStackLayout Spacing="{StaticResource LayoutSpacing}" Padding="{StaticResource LayoutPadding}">
                <sf:SfTextInputLayout Hint="Task">
                    <Entry
                        Text="{Binding Title}"
                        SemanticProperties.Description="Title" />
                </sf:SfTextInputLayout>

                <sf:SfTextInputLayout Hint="Completed">
                    <CheckBox
                        HorizontalOptions="End"
                        IsChecked="{Binding IsCompleted}"
                        SemanticProperties.Description="Status"
                        SemanticProperties.Hint="Indicates if this task is completed" />
                </sf:SfTextInputLayout>

                <sf:SfTextInputLayout 
                    IsVisible="{Binding IsExistingProject}" 
                    Hint="Project">
                    <Picker 
                        ItemsSource="{Binding Projects}"
                        ItemDisplayBinding="{Binding Name, x:DataType=models:Project}"
                        SelectedItem="{Binding Project}"
                        SelectedIndex="{Binding SelectedProjectIndex}"
                        SemanticProperties.Description="Project"
                        SemanticProperties.Hint="Which project this task belongs to" />
                </sf:SfTextInputLayout>

                <Button 
                    HeightRequest="{OnIdiom 44, Desktop=60}"
                    Text="Save"
                    Command="{Binding SaveCommand}"/>
            </VerticalStackLayout>
        </ScrollView>
    </Grid>
</ContentPage>
