using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MauiApp10.Models;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;

namespace MauiApp10.PageModels;

/// <summary>
/// Page model for photo capture and gallery functionality
/// </summary>
public partial class PhotoCapturePageModel : ObservableObject
{
    private readonly ILogger<PhotoCapturePageModel> logger;
    private readonly ICameraService cameraService;
    private readonly IPhotoStorageService photoStorageService;
    private readonly PhotoRepository photoRepository;
    private readonly IErrorHandler errorHandler;

    [ObservableProperty]
    private ObservableCollection<Photo> photos = [];

    [ObservableProperty]
    private bool isBusy;

    [ObservableProperty]
    private bool isRefreshing = false;

    [ObservableProperty]
    private bool isCameraAvailable = false;

    [ObservableProperty]
    private string statusMessage = "Loading photos...";

    [ObservableProperty]
    private int totalPhotos = 0;

    [ObservableProperty]
    private string storageUsed = "0 MB";

    [ObservableProperty]
    private Photo? selectedPhoto;

    public PhotoCapturePageModel(
        ILogger<PhotoCapturePageModel> logger,
        ICameraService cameraService,
        IPhotoStorageService photoStorageService,
        PhotoRepository photoRepository,
        IErrorHandler errorHandler)
    {
        this.logger = logger;
        this.cameraService = cameraService;
        this.photoStorageService = photoStorageService;
        this.photoRepository = photoRepository;
        this.errorHandler = errorHandler;
    }

    [RelayCommand]
    private async Task Appearing()
    {
        try
        {
            IsBusy = true;
            StatusMessage = "Checking camera availability...";

            IsCameraAvailable = await cameraService.IsCameraAvailableAsync();

            await LoadPhotos();
            await UpdateStorageInfo();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during page appearing");
            errorHandler.HandleError(ex);
        }
        finally
        {
            IsBusy = false;
        }
    }

    [RelayCommand]
    private async Task Refresh()
    {
        try
        {
            IsRefreshing = true;
            await LoadPhotos();
            await UpdateStorageInfo();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during refresh");
            errorHandler.HandleError(ex);
        }
        finally
        {
            IsRefreshing = false;
        }
    }

    [RelayCommand]
    private async Task CapturePhoto()
    {
        try
        {
            if (!IsCameraAvailable)
            {
                StatusMessage = "Camera is not available on this device";
                return;
            }

            IsBusy = true;
            StatusMessage = "Opening camera...";

            var capturedPhoto = await cameraService.CapturePhotoAsync();
            if (capturedPhoto == null)
            {
                StatusMessage = $"Photo capture was cancelled (at {DateTime.Now.ToString("hh:mm:ss")})";
                return;
            }

            StatusMessage = "Processing photo...";

            // Get the photo data from temporary storage
            var photoData = GetCapturedPhotoData(capturedPhoto);
            if (photoData == null)
            {
                StatusMessage = "Failed to read captured photo";
                return;
            }

            // Clean up the description (remove temp ID)
            if (capturedPhoto.Description.StartsWith("TEMP_ID:"))
            {
                var parts = capturedPhoto.Description.Split('|', 2);
                capturedPhoto.Description = parts.Length > 1 ? parts[1] : string.Empty;
            }

            // Store the photo securely
            var storedPhoto = await photoStorageService.StorePhotoAsync(capturedPhoto, photoData);
            if (storedPhoto == null)
            {
                StatusMessage = "Failed to store photo securely";
                return;
            }

            // Save to database
            await photoRepository.SaveItemAsync(storedPhoto);

            StatusMessage = "Photo saved successfully!";

            // Refresh the photo list
            await LoadPhotos();
            await UpdateStorageInfo();

            logger.LogInformation("Successfully captured and stored photo: {PhotoId}", storedPhoto.ID);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error capturing photo");
            errorHandler.HandleError(ex);
            StatusMessage = "Failed to capture photo";
        }
        finally
        {
            IsBusy = false;
        }
    }

    [RelayCommand]
    private async Task ViewPhoto(Photo photo)
    {
        try
        {
            if (photo == null) return;

            SelectedPhoto = photo;

            // Navigate to full-screen photo viewer
            await Shell.Current.GoToAsync($"photoviewer?id={photo.ID}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error viewing photo: {PhotoId}", photo?.ID);
            errorHandler.HandleError(ex);
        }
    }

    [RelayCommand]
    private async Task DeletePhoto(Photo photo)
    {
        try
        {
            if (photo == null) return;

            var confirmed = await Shell.Current.DisplayAlert(
                "Delete Photo",
                $"Are you sure you want to delete '{photo.DisplayTitle}'? This action cannot be undone.",
                "Delete",
                "Cancel");

            if (!confirmed) return;

            IsBusy = true;
            StatusMessage = "Deleting photo...";

            // Delete from database
            await photoRepository.DeleteItemAsync(photo);

            // Delete files from storage
            await photoStorageService.DeletePhotoFilesAsync(photo);

            // Remove from collection
            Photos.Remove(photo);

            StatusMessage = "Photo deleted successfully";
            await UpdateStorageInfo();

            logger.LogInformation("Successfully deleted photo: {PhotoId}", photo.ID);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error deleting photo: {PhotoId}", photo?.ID);
            errorHandler.HandleError(ex);
            StatusMessage = "Failed to delete photo";
        }
        finally
        {
            IsBusy = false;
        }
    }

    [RelayCommand]
    private async Task ToggleFavorite(Photo photo)
    {
        try
        {
            if (photo == null) return;

            photo.IsFavorite = !photo.IsFavorite;
            await photoRepository.SaveItemAsync(photo);

            logger.LogInformation("Toggled favorite status for photo: {PhotoId}, IsFavorite: {IsFavorite}",
                photo.ID, photo.IsFavorite);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error toggling favorite for photo: {PhotoId}", photo?.ID);
            errorHandler.HandleError(ex);
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task LoadPhotos()
    {
        try
        {
            StatusMessage = "Loading photos...";

            var photoList = await photoRepository.ListAsync();
            Photos.Clear();

            foreach (var photo in photoList)
            {
                Photos.Add(photo);
            }

            TotalPhotos = Photos.Count;
            StatusMessage = TotalPhotos == 0 ? "No photos yet. Tap the camera button to capture your first photo!"
                                            : $"Loaded {TotalPhotos} photos";

            logger.LogInformation("Loaded {PhotoCount} photos", TotalPhotos);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error loading photos");
            StatusMessage = "Failed to load photos";
            throw;
        }
    }

    private async Task UpdateStorageInfo()
    {
        try
        {
            var totalBytes = await photoStorageService.GetTotalStorageUsedAsync();
            StorageUsed = FormatBytes(totalBytes);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating storage info");
            StorageUsed = "Unknown";
        }
    }

    private byte[]? GetCapturedPhotoData(Photo photo)
    {
        try
        {
            // Extract temp ID from description
            if (photo.Description.StartsWith("TEMP_ID:"))
            {
                var tempIdEnd = photo.Description.IndexOf('|');
                if (tempIdEnd > 0)
                {
                    var tempId = photo.Description.Substring(8, tempIdEnd - 8); // Skip "TEMP_ID:"
                    return CameraService.GetAndRemoveTempPhotoData(tempId);
                }
            }

            logger.LogWarning("No temp ID found in photo description");
            return null;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting captured photo data");
            return null;
        }
    }

    private static string FormatBytes(long bytes)
    {
        if (bytes < 1024)
            return $"{bytes} B";
        if (bytes < 1024 * 1024)
            return $"{bytes / 1024.0:F1} KB";
        if (bytes < 1024 * 1024 * 1024)
            return $"{bytes / (1024.0 * 1024.0):F1} MB";
        return $"{bytes / (1024.0 * 1024.0 * 1024.0):F1} GB";
    }
}
