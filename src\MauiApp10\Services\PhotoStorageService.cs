using Microsoft.Extensions.Logging;
using MauiApp10.Data;
using MauiApp10.Models;
using SkiaSharp;

namespace MauiApp10.Services;

/// <summary>
/// Service for secure photo storage operations with encryption and thumbnail generation
/// </summary>
public class PhotoStorageService : IPhotoStorageService
{
    private readonly ILogger<PhotoStorageService> logger;
    private readonly IEncryptionService encryptionService;

    public PhotoStorageService(ILogger<PhotoStorageService> logger, IEncryptionService encryptionService)
    {
        this.logger = logger;
        this.encryptionService = encryptionService;

        // Ensure storage directories exist
        Constants.EnsureDirectoriesExist();
    }

    public async Task<Photo?> StorePhotoAsync(Photo photo, byte[] imageData)
    {
        try
        {
            // Generate secure file names
            var photoFileName = this.encryptionService.GenerateSecureFileName(photo.OriginalFileName);
            var thumbnailFileName = this.encryptionService.GenerateSecureFileName($"thumb_{photo.OriginalFileName}");

            var photoFilePath = Path.Combine(Constants.PhotosDirectory, photoFileName);
            var thumbnailFilePath = Path.Combine(Constants.ThumbnailsDirectory, thumbnailFileName);

            // Generate thumbnail
            var thumbnailData = await GenerateThumbnailAsync(imageData);

            // Encrypt and store the original photo
            var tempPhotoPath = Path.GetTempFileName();
            await File.WriteAllBytesAsync(tempPhotoPath, imageData);

            var photoStored = await this.encryptionService.EncryptFileAsync(tempPhotoPath, photoFilePath);
            File.Delete(tempPhotoPath); // Clean up temp file

            if (!photoStored)
            {
                this.logger.LogError("Failed to store encrypted photo file");
                return null;
            }

            // Encrypt and store the thumbnail
            var tempThumbnailPath = Path.GetTempFileName();
            await File.WriteAllBytesAsync(tempThumbnailPath, thumbnailData);

            var thumbnailStored = await this.encryptionService.EncryptFileAsync(tempThumbnailPath, thumbnailFilePath);
            File.Delete(tempThumbnailPath); // Clean up temp file

            if (!thumbnailStored)
            {
                this.logger.LogError("Failed to store encrypted thumbnail file");
                // Clean up the photo file if thumbnail storage failed
                if (File.Exists(photoFilePath))
                    File.Delete(photoFilePath);
                return null;
            }

            // Update photo object with file paths
            photo.FilePath = photoFilePath;
            photo.ThumbnailPath = thumbnailFilePath;

            this.logger.LogInformation("Successfully stored photo: {FileName} -> {PhotoPath}, Thumbnail: {ThumbnailPath}",
                photo.OriginalFileName, photoFilePath, thumbnailFilePath);

            return photo;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error storing photo: {FileName}", photo.OriginalFileName);
            return null;
        }
    }

    public async Task<byte[]?> GetPhotoDataAsync(Photo photo)
    {
        try
        {
            if (string.IsNullOrEmpty(photo.FilePath) || !File.Exists(photo.FilePath))
            {
                this.logger.LogWarning("Photo file does not exist: {FilePath}", photo.FilePath);
                return null;
            }

            var tempPath = Path.GetTempFileName();
            var decrypted = await this.encryptionService.DecryptFileAsync(photo.FilePath, tempPath);

            if (!decrypted)
            {
                this.logger.LogError("Failed to decrypt photo file: {FilePath}", photo.FilePath);
                return null;
            }

            var data = await File.ReadAllBytesAsync(tempPath);
            File.Delete(tempPath); // Clean up temp file

            return data;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error retrieving photo data: {FilePath}", photo.FilePath);
            return null;
        }
    }

    public async Task<byte[]?> GetThumbnailDataAsync(Photo photo)
    {
        try
        {
            if (string.IsNullOrEmpty(photo.ThumbnailPath) || !File.Exists(photo.ThumbnailPath))
            {
                this.logger.LogWarning("Thumbnail file does not exist: {ThumbnailPath}", photo.ThumbnailPath);
                return null;
            }

            var tempPath = Path.GetTempFileName();
            var decrypted = await this.encryptionService.DecryptFileAsync(photo.ThumbnailPath, tempPath);

            if (!decrypted)
            {
                this.logger.LogError("Failed to decrypt thumbnail file: {ThumbnailPath}", photo.ThumbnailPath);
                return null;
            }

            var data = await File.ReadAllBytesAsync(tempPath);
            File.Delete(tempPath); // Clean up temp file

            return data;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error retrieving thumbnail data: {ThumbnailPath}", photo.ThumbnailPath);
            return null;
        }
    }

    public async Task<bool> DeletePhotoFilesAsync(Photo photo)
    {
        try
        {
            var success = true;

            // Delete photo file
            if (!string.IsNullOrEmpty(photo.FilePath) && File.Exists(photo.FilePath))
            {
                try
                {
                    File.Delete(photo.FilePath);
                    this.logger.LogInformation("Deleted photo file: {FilePath}", photo.FilePath);
                }
                catch (Exception ex)
                {
                    this.logger.LogError(ex, "Failed to delete photo file: {FilePath}", photo.FilePath);
                    success = false;
                }
            }

            // Delete thumbnail file
            if (!string.IsNullOrEmpty(photo.ThumbnailPath) && File.Exists(photo.ThumbnailPath))
            {
                try
                {
                    File.Delete(photo.ThumbnailPath);
                    this.logger.LogInformation("Deleted thumbnail file: {ThumbnailPath}", photo.ThumbnailPath);
                }
                catch (Exception ex)
                {
                    this.logger.LogError(ex, "Failed to delete thumbnail file: {ThumbnailPath}", photo.ThumbnailPath);
                    success = false;
                }
            }

            return success;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error deleting photo files for photo ID: {PhotoId}", photo.ID);
            return false;
        }
    }

    public async Task<byte[]> GenerateThumbnailAsync(byte[] imageData, int maxWidth = 200, int maxHeight = 200)
    {
        return await Task.Run(() =>
        {
            try
            {
                using var inputStream = new MemoryStream(imageData);
                using var codec = SKCodec.Create(inputStream);

                if (codec == null)
                {
                    this.logger.LogError("Failed to create codec for thumbnail generation");
                    return imageData; // Return original if we can't process it
                }

                var info = codec.Info;

                // Calculate new dimensions maintaining aspect ratio
                var aspectRatio = (float)info.Width / info.Height;
                int newWidth, newHeight;

                if (aspectRatio > 1) // Landscape
                {
                    newWidth = Math.Min(maxWidth, info.Width);
                    newHeight = (int)(newWidth / aspectRatio);
                }
                else // Portrait or square
                {
                    newHeight = Math.Min(maxHeight, info.Height);
                    newWidth = (int)(newHeight * aspectRatio);
                }

                // Create thumbnail
                using var original = SKBitmap.Decode(codec);
                using var thumbnail = original.Resize(new SKImageInfo(newWidth, newHeight), SKFilterQuality.High);
                using var image = SKImage.FromBitmap(thumbnail);
                using var data = image.Encode(SKEncodedImageFormat.Jpeg, 85); // 85% quality for good compression

                return data.ToArray();
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error generating thumbnail");
                return imageData; // Return original if thumbnail generation fails
            }
        });
    }

    public Task<long> GetTotalStorageUsedAsync()
    {
        try
        {
            long totalSize = 0;

            // Calculate photos directory size
            if (Directory.Exists(Constants.PhotosDirectory))
            {
                var photoFiles = Directory.GetFiles(Constants.PhotosDirectory);
                foreach (var file in photoFiles)
                {
                    var fileInfo = new FileInfo(file);
                    totalSize += fileInfo.Length;
                }
            }

            // Calculate thumbnails directory size
            if (Directory.Exists(Constants.ThumbnailsDirectory))
            {
                var thumbnailFiles = Directory.GetFiles(Constants.ThumbnailsDirectory);
                foreach (var file in thumbnailFiles)
                {
                    var fileInfo = new FileInfo(file);
                    totalSize += fileInfo.Length;
                }
            }

            return Task.FromResult(totalSize);
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error calculating total storage used");
            return Task.FromResult(0L);
        }
    }

    public Task<int> CleanupOrphanedFilesAsync()
    {
        try
        {
            // This would require access to PhotoRepository to check which files are referenced
            // For now, we'll implement a basic cleanup that could be enhanced later
            var cleanedCount = 0;

            // Get all files in both directories
            var photoFiles = Directory.Exists(Constants.PhotosDirectory)
                ? Directory.GetFiles(Constants.PhotosDirectory)
                : Array.Empty<string>();

            var thumbnailFiles = Directory.Exists(Constants.ThumbnailsDirectory)
                ? Directory.GetFiles(Constants.ThumbnailsDirectory)
                : Array.Empty<string>();

            // For now, just log the file counts - actual cleanup would need database integration
            this.logger.LogInformation("Found {PhotoCount} photo files and {ThumbnailCount} thumbnail files",
                photoFiles.Length, thumbnailFiles.Length);

            // TODO: Implement actual orphan detection by comparing with database entries
            // This would require injecting PhotoRepository or creating a separate cleanup service

            return Task.FromResult(cleanedCount);
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error during orphaned files cleanup");
            return Task.FromResult(0);
        }
    }
}
