using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MauiApp10.Data;
using MauiApp10.Models;
using MauiApp10.Services;

namespace MauiApp10.PageModels;

/// <summary>
/// Page model for viewing individual photos in full screen
/// </summary>
public partial class PhotoViewerPageModel : ObservableObject, IQueryAttributable
{
    private readonly ILogger<PhotoViewerPageModel> logger;
    private readonly PhotoRepository photoRepository;
    private readonly IPhotoStorageService photoStorageService;
    private readonly IErrorHandler errorHandler;

    [ObservableProperty]
    private Photo? photo;

    [ObservableProperty]
    private ImageSource? photoImageSource;

    [ObservableProperty]
    private bool isBusy = false;

    [ObservableProperty]
    private bool showInfo = true;

    [ObservableProperty]
    private bool showDetailedInfo = false;

    public PhotoViewerPageModel(
        ILogger<PhotoViewerPageModel> logger,
        PhotoRepository photoRepository,
        IPhotoStorageService photoStorageService,
        IErrorHandler errorHandler)
    {
        this.logger = logger;
        this.photoRepository = photoRepository;
        this.photoStorageService = photoStorageService;
        this.errorHandler = errorHandler;
    }

    public void ApplyQueryAttributes(IDictionary<string, object> query)
    {
        if (query.TryGetValue("id", out var idObj) && int.TryParse(idObj.ToString(), out var photoId))
        {
            _ = LoadPhoto(photoId);
        }
    }

    [RelayCommand]
    private async Task Appearing()
    {
        // Photo loading is handled by ApplyQueryAttributes
        await Task.CompletedTask;
    }

    [RelayCommand]
    private void ToggleInfo()
    {
        ShowDetailedInfo = !ShowDetailedInfo;
    }

    private async Task LoadPhoto(int photoId)
    {
        try
        {
            IsBusy = true;

            Photo = await this.photoRepository.GetAsync(photoId);
            if (Photo == null)
            {
                this.logger.LogWarning("Photo not found: {PhotoId}", photoId);
                await Shell.Current.DisplayAlert("Error", "Photo not found", "OK");
                await Shell.Current.GoToAsync("..");
                return;
            }

            await LoadPhotoImage();

            this.logger.LogInformation("Loaded photo for viewing: {PhotoId}", photoId);
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error loading photo: {PhotoId}", photoId);
            this.errorHandler.HandleError(ex);
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task LoadPhotoImage()
    {
        try
        {
            if (Photo == null) return;

            var photoData = await this.photoStorageService.GetPhotoDataAsync(Photo);
            if (photoData != null)
            {
                PhotoImageSource = ImageSource.FromStream(() => new MemoryStream(photoData));
            }
            else
            {
                this.logger.LogWarning("Failed to load photo data for: {PhotoId}", Photo.ID);
                // Could show a placeholder image here
            }
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error loading photo image: {PhotoId}", Photo?.ID);
        }
    }
}
