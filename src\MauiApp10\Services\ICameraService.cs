using MauiApp10.Models;

namespace MauiApp10.Services;

/// <summary>
/// Service for camera operations and photo capture
/// </summary>
public interface ICameraService
{
    /// <summary>
    /// Checks if camera is available on the device
    /// </summary>
    /// <returns>True if camera is available, false otherwise</returns>
    Task<bool> IsCameraAvailableAsync();

    /// <summary>
    /// Requests camera permissions from the user
    /// </summary>
    /// <returns>True if permissions are granted, false otherwise</returns>
    Task<bool> RequestCameraPermissionsAsync();

    /// <summary>
    /// Captures a photo using the device camera
    /// </summary>
    /// <returns>A Photo object with captured image data, or null if capture failed</returns>
    Task<Photo?> CapturePhotoAsync();

    /// <summary>
    /// Captures a photo using the device camera with custom options
    /// </summary>
    /// <param name="title">Optional title for the photo</param>
    /// <param name="description">Optional description for the photo</param>
    /// <param name="includeLocation">Whether to include GPS location data</param>
    /// <returns>A Photo object with captured image data, or null if capture failed</returns>
    Task<Photo?> CapturePhotoAsync(string? title = null, string? description = null, bool includeLocation = true);

    /// <summary>
    /// Gets the current location if permissions are available
    /// </summary>
    /// <returns>Tuple of latitude and longitude, or null if location is not available</returns>
    Task<(double? Latitude, double? Longitude)> GetCurrentLocationAsync();
}
