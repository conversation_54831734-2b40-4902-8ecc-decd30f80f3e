using Microsoft.Data.Sqlite;
using Microsoft.Extensions.Logging;
using MauiApp10.Models;

namespace MauiApp10.Data;

/// <summary>
/// Repository for managing photo metadata in the database
/// </summary>
public class PhotoRepository
{
    private readonly ILogger<PhotoRepository> logger;
    private bool hasBeenInitialized = false;

    public PhotoRepository(ILogger<PhotoRepository> logger)
    {
        this.logger = logger;
    }

    /// <summary>
    /// Initializes the database connection and creates the Photo table if it does not exist
    /// </summary>
    private async Task Init()
    {
        if (this.hasBeenInitialized)
            return;

        await using var connection = new SqliteConnection(Constants.EncryptedDatabasePath);
        await connection.OpenAsync();

        try
        {
            var createTableCmd = connection.CreateCommand();
            createTableCmd.CommandText = @"
                CREATE TABLE IF NOT EXISTS Photo (
                    ID INTEGER PRIMARY KEY AUTOINCREMENT,
                    Title TEXT NOT NULL,
                    Description TEXT NOT NULL,
                    FilePath TEXT NOT NULL,
                    ThumbnailPath TEXT NOT NULL,
                    OriginalFileName TEXT NOT NULL,
                    FileSizeBytes INTEGER NOT NULL,
                    Width INTEGER NOT NULL,
                    Height INTEGER NOT NULL,
                    CapturedAt TEXT NOT NULL,
                    CreatedAt TEXT NOT NULL,
                    UpdatedAt TEXT NOT NULL,
                    Latitude REAL NULL,
                    Longitude REAL NULL,
                    MimeType TEXT NOT NULL,
                    IsFavorite INTEGER NOT NULL DEFAULT 0
                );";
            await createTableCmd.ExecuteNonQueryAsync();

            // Create index for faster queries
            var createIndexCmd = connection.CreateCommand();
            createIndexCmd.CommandText = @"
                CREATE INDEX IF NOT EXISTS IX_Photo_CapturedAt ON Photo(CapturedAt);
                CREATE INDEX IF NOT EXISTS IX_Photo_IsFavorite ON Photo(IsFavorite);";
            await createIndexCmd.ExecuteNonQueryAsync();
        }
        catch (Exception e)
        {
            this.logger.LogError(e, "Error creating Photo table");
            throw;
        }

        this.hasBeenInitialized = true;
    }

    /// <summary>
    /// Retrieves a list of all photos from the database, ordered by capture date descending
    /// </summary>
    /// <returns>A list of Photo objects</returns>
    public async Task<List<Photo>> ListAsync()
    {
        await Init();
        await using var connection = new SqliteConnection(Constants.EncryptedDatabasePath);
        await connection.OpenAsync();

        var selectCmd = connection.CreateCommand();
        selectCmd.CommandText = "SELECT * FROM Photo ORDER BY CapturedAt DESC";
        var photos = new List<Photo>();

        await using var reader = await selectCmd.ExecuteReaderAsync();
        while (await reader.ReadAsync())
        {
            photos.Add(MapFromReader(reader));
        }

        return photos;
    }

    /// <summary>
    /// Retrieves favorite photos only
    /// </summary>
    /// <returns>A list of favorite Photo objects</returns>
    public async Task<List<Photo>> ListFavoritesAsync()
    {
        await Init();
        await using var connection = new SqliteConnection(Constants.EncryptedDatabasePath);
        await connection.OpenAsync();

        var selectCmd = connection.CreateCommand();
        selectCmd.CommandText = "SELECT * FROM Photo WHERE IsFavorite = 1 ORDER BY CapturedAt DESC";
        var photos = new List<Photo>();

        await using var reader = await selectCmd.ExecuteReaderAsync();
        while (await reader.ReadAsync())
        {
            photos.Add(MapFromReader(reader));
        }

        return photos;
    }

    /// <summary>
    /// Retrieves a specific photo by its ID
    /// </summary>
    /// <param name="id">The ID of the photo</param>
    /// <returns>A Photo object if found; otherwise, null</returns>
    public async Task<Photo?> GetAsync(int id)
    {
        await Init();
        await using var connection = new SqliteConnection(Constants.EncryptedDatabasePath);
        await connection.OpenAsync();

        var selectCmd = connection.CreateCommand();
        selectCmd.CommandText = "SELECT * FROM Photo WHERE ID = @id";
        selectCmd.Parameters.AddWithValue("@id", id);

        await using var reader = await selectCmd.ExecuteReaderAsync();
        if (await reader.ReadAsync())
        {
            return MapFromReader(reader);
        }

        return null;
    }

    /// <summary>
    /// Saves a photo to the database. If the photo ID is 0, a new photo is created; otherwise, the existing photo is updated
    /// </summary>
    /// <param name="item">The photo to save</param>
    /// <returns>The ID of the saved photo</returns>
    public async Task<int> SaveItemAsync(Photo item)
    {
        await Init();
        await using var connection = new SqliteConnection(Constants.EncryptedDatabasePath);
        await connection.OpenAsync();

        var saveCmd = connection.CreateCommand();
        if (item.ID == 0)
        {
            // Insert new photo
            item.CreatedAt = DateTime.UtcNow;
            item.UpdatedAt = item.CreatedAt;

            saveCmd.CommandText = @"
                INSERT INTO Photo (Title, Description, FilePath, ThumbnailPath, OriginalFileName, 
                                 FileSizeBytes, Width, Height, CapturedAt, CreatedAt, UpdatedAt, 
                                 Latitude, Longitude, MimeType, IsFavorite)
                VALUES (@Title, @Description, @FilePath, @ThumbnailPath, @OriginalFileName, 
                        @FileSizeBytes, @Width, @Height, @CapturedAt, @CreatedAt, @UpdatedAt, 
                        @Latitude, @Longitude, @MimeType, @IsFavorite);
                SELECT last_insert_rowid();";
        }
        else
        {
            // Update existing photo
            item.UpdatedAt = DateTime.UtcNow;

            saveCmd.CommandText = @"
                UPDATE Photo SET Title = @Title, Description = @Description, FilePath = @FilePath, 
                               ThumbnailPath = @ThumbnailPath, OriginalFileName = @OriginalFileName,
                               FileSizeBytes = @FileSizeBytes, Width = @Width, Height = @Height, 
                               CapturedAt = @CapturedAt, UpdatedAt = @UpdatedAt, Latitude = @Latitude, 
                               Longitude = @Longitude, MimeType = @MimeType, IsFavorite = @IsFavorite
                WHERE ID = @ID";
            saveCmd.Parameters.AddWithValue("@ID", item.ID);
        }

        AddPhotoParameters(saveCmd, item);

        if (item.ID == 0)
        {
            var result = await saveCmd.ExecuteScalarAsync();
            item.ID = Convert.ToInt32(result);
            return item.ID;
        }
        else
        {
            await saveCmd.ExecuteNonQueryAsync();
            return item.ID;
        }
    }

    /// <summary>
    /// Deletes a photo from the database
    /// </summary>
    /// <param name="item">The photo to delete</param>
    /// <returns>The number of rows affected</returns>
    public async Task<int> DeleteItemAsync(Photo item)
    {
        await Init();
        await using var connection = new SqliteConnection(Constants.EncryptedDatabasePath);
        await connection.OpenAsync();

        var deleteCmd = connection.CreateCommand();
        deleteCmd.CommandText = "DELETE FROM Photo WHERE ID = @id";
        deleteCmd.Parameters.AddWithValue("@id", item.ID);

        return await deleteCmd.ExecuteNonQueryAsync();
    }

    /// <summary>
    /// Gets the total count of photos
    /// </summary>
    /// <returns>Total number of photos</returns>
    public async Task<int> GetCountAsync()
    {
        await Init();
        await using var connection = new SqliteConnection(Constants.EncryptedDatabasePath);
        await connection.OpenAsync();

        var countCmd = connection.CreateCommand();
        countCmd.CommandText = "SELECT COUNT(*) FROM Photo";

        var result = await countCmd.ExecuteScalarAsync();
        return Convert.ToInt32(result);
    }

    /// <summary>
    /// Maps a SqliteDataReader to a Photo object
    /// </summary>
    private static Photo MapFromReader(SqliteDataReader reader)
    {
        return new Photo
        {
            ID = reader.GetInt32(0),
            Title = reader.GetString(1),
            Description = reader.GetString(2),
            FilePath = reader.GetString(3),
            ThumbnailPath = reader.GetString(4),
            OriginalFileName = reader.GetString(5),
            FileSizeBytes = reader.GetInt64(6),
            Width = reader.GetInt32(7),
            Height = reader.GetInt32(8),
            CapturedAt = DateTime.Parse(reader.GetString(9)),
            CreatedAt = DateTime.Parse(reader.GetString(10)),
            UpdatedAt = DateTime.Parse(reader.GetString(11)),
            Latitude = reader.IsDBNull(12) ? null : reader.GetDouble(12),
            Longitude = reader.IsDBNull(13) ? null : reader.GetDouble(13),
            MimeType = reader.GetString(14),
            IsFavorite = reader.GetInt32(15) == 1
        };
    }

    /// <summary>
    /// Adds parameters to a SqliteCommand for a Photo object
    /// </summary>
    private static void AddPhotoParameters(SqliteCommand cmd, Photo photo)
    {
        cmd.Parameters.AddWithValue("@Title", photo.Title);
        cmd.Parameters.AddWithValue("@Description", photo.Description);
        cmd.Parameters.AddWithValue("@FilePath", photo.FilePath);
        cmd.Parameters.AddWithValue("@ThumbnailPath", photo.ThumbnailPath);
        cmd.Parameters.AddWithValue("@OriginalFileName", photo.OriginalFileName);
        cmd.Parameters.AddWithValue("@FileSizeBytes", photo.FileSizeBytes);
        cmd.Parameters.AddWithValue("@Width", photo.Width);
        cmd.Parameters.AddWithValue("@Height", photo.Height);
        cmd.Parameters.AddWithValue("@CapturedAt", photo.CapturedAt.ToString("O"));
        cmd.Parameters.AddWithValue("@CreatedAt", photo.CreatedAt.ToString("O"));
        cmd.Parameters.AddWithValue("@UpdatedAt", photo.UpdatedAt.ToString("O"));
        cmd.Parameters.AddWithValue("@Latitude", photo.Latitude.HasValue ? photo.Latitude.Value : DBNull.Value);
        cmd.Parameters.AddWithValue("@Longitude", photo.Longitude.HasValue ? photo.Longitude.Value : DBNull.Value);
        cmd.Parameters.AddWithValue("@MimeType", photo.MimeType);
        cmd.Parameters.AddWithValue("@IsFavorite", photo.IsFavorite ? 1 : 0);
    }
}
