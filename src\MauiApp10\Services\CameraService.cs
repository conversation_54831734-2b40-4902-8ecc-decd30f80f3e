using Microsoft.Extensions.Logging;
using MauiApp10.Models;
using SkiaSharp;

namespace MauiApp10.Services;

/// <summary>
/// Service for camera operations and photo capture using MAUI Essentials
/// </summary>
public class CameraService : ICameraService
{
    private readonly ILogger<CameraService> logger;

    public CameraService(ILogger<CameraService> logger)
    {
        this.logger = logger;
    }

    public Task<bool> IsCameraAvailableAsync()
    {
        try
        {
            return Task.FromResult(MediaPicker.Default.IsCaptureSupported);
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error checking camera availability");
            return Task.FromResult(false);
        }
    }

    public async Task<bool> RequestCameraPermissionsAsync()
    {
        try
        {
            var cameraStatus = await Permissions.RequestAsync<Permissions.Camera>();
            var storageStatus = await Permissions.RequestAsync<Permissions.StorageWrite>();

            return cameraStatus == PermissionStatus.Granted && storageStatus == PermissionStatus.Granted;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error requesting camera permissions");
            return false;
        }
    }

    public async Task<Photo?> CapturePhotoAsync()
    {
        return await CapturePhotoAsync(null, null, true);
    }

    public async Task<Photo?> CapturePhotoAsync(string? title = null, string? description = null, bool includeLocation = true)
    {
        try
        {
            // Check permissions first
            if (!await RequestCameraPermissionsAsync())
            {
                this.logger.LogWarning("Camera permissions not granted");
                return null;
            }

            // Capture photo using MediaPicker
            var photo = await MediaPicker.Default.CapturePhotoAsync();
            if (photo == null)
            {
                this.logger.LogInformation("Photo capture was cancelled by user");
                return null;
            }

            // Read the photo data
            using var stream = await photo.OpenReadAsync();
            var photoData = new byte[stream.Length];
            await stream.ReadAsync(photoData, 0, photoData.Length);

            // Get image dimensions using SkiaSharp
            var (width, height) = GetImageDimensions(photoData);

            // Get location if requested
            var (latitude, longitude) = includeLocation ? await GetCurrentLocationAsync() : (null, null);

            // Create Photo object with metadata and store the photo data temporarily
            var photoModel = new Photo
            {
                Title = title ?? $"Photo {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                Description = description ?? string.Empty,
                OriginalFileName = photo.FileName ?? $"photo_{DateTime.Now:yyyyMMdd_HHmmss}.jpg",
                FileSizeBytes = photoData.Length,
                Width = width,
                Height = height,
                CapturedAt = DateTime.UtcNow,
                Latitude = latitude,
                Longitude = longitude,
                MimeType = GetMimeType(photo.FileName ?? "photo.jpg")
            };

            // Store photo data in a temporary property for retrieval
            // We'll use a static dictionary to temporarily hold the data
            var tempId = Guid.NewGuid().ToString();
            TempPhotoStorage[tempId] = photoData;

            // Store the temp ID in the description temporarily (will be cleared after storage)
            photoModel.Description = $"TEMP_ID:{tempId}|{photoModel.Description}";

            this.logger.LogInformation("Successfully captured photo: {FileName}, Size: {Size} bytes, Dimensions: {Width}x{Height}",
                photoModel.OriginalFileName, photoModel.FileSizeBytes, photoModel.Width, photoModel.Height);

            return photoModel;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error capturing photo");
            return null;
        }
    }

    // Temporary storage for photo data between capture and storage
    private static readonly Dictionary<string, byte[]> TempPhotoStorage = new();

    /// <summary>
    /// Retrieves and removes temporary photo data
    /// </summary>
    public static byte[]? GetAndRemoveTempPhotoData(string tempId)
    {
        if (TempPhotoStorage.TryGetValue(tempId, out var data))
        {
            TempPhotoStorage.Remove(tempId);
            return data;
        }
        return null;
    }

    public async Task<(double? Latitude, double? Longitude)> GetCurrentLocationAsync()
    {
        try
        {
            var locationStatus = await Permissions.RequestAsync<Permissions.LocationWhenInUse>();
            if (locationStatus != PermissionStatus.Granted)
            {
                this.logger.LogWarning("Location permissions not granted");
                return (null, null);
            }

            var request = new GeolocationRequest
            {
                DesiredAccuracy = GeolocationAccuracy.Medium,
                Timeout = TimeSpan.FromSeconds(10)
            };

            var location = await Geolocation.Default.GetLocationAsync(request);
            if (location != null)
            {
                this.logger.LogInformation("Location obtained: {Latitude}, {Longitude}", location.Latitude, location.Longitude);
                return (location.Latitude, location.Longitude);
            }

            return (null, null);
        }
        catch (Exception ex)
        {
            this.logger.LogWarning(ex, "Error getting current location");
            return (null, null);
        }
    }

    /// <summary>
    /// Gets image dimensions using SkiaSharp
    /// </summary>
    private (int Width, int Height) GetImageDimensions(byte[] imageData)
    {
        try
        {
            using var codec = SKCodec.Create(new MemoryStream(imageData));
            if (codec != null)
            {
                return (codec.Info.Width, codec.Info.Height);
            }
        }
        catch (Exception ex)
        {
            this.logger.LogWarning(ex, "Error getting image dimensions, using defaults");
        }

        // Return default dimensions if we can't determine the actual size
        return (1920, 1080);
    }

    /// <summary>
    /// Determines MIME type based on file extension
    /// </summary>
    private static string GetMimeType(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".bmp" => "image/bmp",
            ".webp" => "image/webp",
            _ => "image/jpeg"
        };
    }
}
