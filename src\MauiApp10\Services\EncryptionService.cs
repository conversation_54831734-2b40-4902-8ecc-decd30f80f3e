using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;

namespace MauiApp10.Services;

/// <summary>
/// Service for encrypting and decrypting files and data using AES-256 encryption
/// </summary>
public class EncryptionService : IEncryptionService
{
    private readonly ILogger<EncryptionService> logger;
    private readonly byte[] encryptionKey;
    private const int KeySize = 256;
    private const int IvSize = 16;

    public EncryptionService(ILogger<EncryptionService> logger)
    {
        this.logger = logger;
        this.encryptionKey = GetOrCreateEncryptionKey();
    }

    /// <summary>
    /// Gets or creates the encryption key, storing it securely in preferences
    /// </summary>
    private byte[] GetOrCreateEncryptionKey()
    {
        try
        {
            // Try to get existing key from secure storage
            var keyString = Preferences.Default.Get("encryption_key", string.Empty);
            
            if (!string.IsNullOrEmpty(keyString))
            {
                return Convert.FromBase64String(keyString);
            }

            // Generate new key if none exists
            using var rng = RandomNumberGenerator.Create();
            var key = new byte[KeySize / 8]; // 32 bytes for AES-256
            rng.GetBytes(key);
            
            // Store the key securely
            Preferences.Default.Set("encryption_key", Convert.ToBase64String(key));
            
            return key;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Failed to get or create encryption key");
            throw;
        }
    }

    public async Task<bool> EncryptFileAsync(string sourceFilePath, string destinationFilePath)
    {
        try
        {
            if (!File.Exists(sourceFilePath))
            {
                this.logger.LogWarning("Source file does not exist: {SourcePath}", sourceFilePath);
                return false;
            }

            var sourceData = await File.ReadAllBytesAsync(sourceFilePath);
            var encryptedData = await EncryptDataAsync(sourceData);
            
            // Ensure destination directory exists
            var destinationDir = Path.GetDirectoryName(destinationFilePath);
            if (!string.IsNullOrEmpty(destinationDir) && !Directory.Exists(destinationDir))
            {
                Directory.CreateDirectory(destinationDir);
            }

            await File.WriteAllBytesAsync(destinationFilePath, encryptedData);
            
            this.logger.LogInformation("Successfully encrypted file: {SourcePath} -> {DestinationPath}", 
                sourceFilePath, destinationFilePath);
            
            return true;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Failed to encrypt file: {SourcePath}", sourceFilePath);
            return false;
        }
    }

    public async Task<bool> DecryptFileAsync(string encryptedFilePath, string destinationFilePath)
    {
        try
        {
            if (!File.Exists(encryptedFilePath))
            {
                this.logger.LogWarning("Encrypted file does not exist: {EncryptedPath}", encryptedFilePath);
                return false;
            }

            var encryptedData = await File.ReadAllBytesAsync(encryptedFilePath);
            var decryptedData = await DecryptDataAsync(encryptedData);
            
            // Ensure destination directory exists
            var destinationDir = Path.GetDirectoryName(destinationFilePath);
            if (!string.IsNullOrEmpty(destinationDir) && !Directory.Exists(destinationDir))
            {
                Directory.CreateDirectory(destinationDir);
            }

            await File.WriteAllBytesAsync(destinationFilePath, decryptedData);
            
            this.logger.LogInformation("Successfully decrypted file: {EncryptedPath} -> {DestinationPath}", 
                encryptedFilePath, destinationFilePath);
            
            return true;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Failed to decrypt file: {EncryptedPath}", encryptedFilePath);
            return false;
        }
    }

    public async Task<byte[]> EncryptDataAsync(byte[] data)
    {
        return await Task.Run(() =>
        {
            try
            {
                using var aes = Aes.Create();
                aes.Key = this.encryptionKey;
                aes.GenerateIV();

                using var encryptor = aes.CreateEncryptor();
                using var msEncrypt = new MemoryStream();
                
                // Write IV to the beginning of the stream
                msEncrypt.Write(aes.IV, 0, aes.IV.Length);
                
                using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                {
                    csEncrypt.Write(data, 0, data.Length);
                }

                return msEncrypt.ToArray();
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Failed to encrypt data");
                throw;
            }
        });
    }

    public async Task<byte[]> DecryptDataAsync(byte[] encryptedData)
    {
        return await Task.Run(() =>
        {
            try
            {
                using var aes = Aes.Create();
                aes.Key = this.encryptionKey;

                // Extract IV from the beginning of the encrypted data
                var iv = new byte[IvSize];
                Array.Copy(encryptedData, 0, iv, 0, IvSize);
                aes.IV = iv;

                using var decryptor = aes.CreateDecryptor();
                using var msDecrypt = new MemoryStream(encryptedData, IvSize, encryptedData.Length - IvSize);
                using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                using var msResult = new MemoryStream();
                
                csDecrypt.CopyTo(msResult);
                return msResult.ToArray();
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Failed to decrypt data");
                throw;
            }
        });
    }

    public string GenerateSecureFileName(string originalFileName)
    {
        try
        {
            var extension = Path.GetExtension(originalFileName);
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            var randomBytes = new byte[8];
            
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomBytes);
            
            var randomString = Convert.ToBase64String(randomBytes)
                .Replace("+", "")
                .Replace("/", "")
                .Replace("=", "");
            
            return $"{timestamp}_{randomString}{extension}.enc";
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Failed to generate secure file name");
            throw;
        }
    }
}
