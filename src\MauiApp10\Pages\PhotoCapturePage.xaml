<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:pagemodels="clr-namespace:MauiApp10.PageModels"
             xmlns:models="clr-namespace:MauiApp10.Models"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             xmlns:vc="clr-namespace:MauiApp10.Utilities"
             x:DataType="pagemodels:PhotoCapturePageModel"
             x:Class="MauiApp10.Pages.PhotoCapturePage"
             x:Name="PhotoCaptureContentPage"
             Title="Photos">

    <ContentPage.Behaviors>
        <toolkit:EventToCommandBehavior
            BindingContext="{Binding Path=BindingContext, Source={x:Reference PhotoCaptureContentPage}, x:DataType=ContentPage}"
            EventName="Appearing"
            Command="{Binding AppearingCommand}"/>
    </ContentPage.Behaviors>

    <ContentPage.Resources>
        <ResourceDictionary>
            <toolkit:InvertedBoolConverter x:Key="InvertedBoolConverter" />
            <vc:BoolToFavoriteIconConverter x:Key="BoolToFavoriteIconConverter" />
            <vc:BoolToFavoriteColorConverter x:Key="BoolToFavoriteColorConverter" />
            <vc:StringToBoolConverter x:Key="StringToBoolConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header with stats and camera button -->
        <Border Grid.Row="0"
                BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray900}}"
                Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackLayout Grid.Column="0"
                             Spacing="4">
                    <Label Text="{Binding TotalPhotos, StringFormat='{0} Photos'}"
                           FontSize="18"
                           FontAttributes="Bold"/>
                    <Label Text="{Binding StorageUsed, StringFormat='Storage: {0}'}"
                           FontSize="12"
                           TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
                </StackLayout>

                <Label Grid.Column="1" Text="{Binding Path=IsBusy, Converter={StaticResource InvertedBoolConverter}}" IsVisible="False" />
                <Label Grid.Column="2" Text="{Binding Path=IsRefreshing}" IsVisible="False" />
                
                <Button Grid.Column="3"
                        Text="📷"
                        FontSize="24"
                        WidthRequest="60"
                        HeightRequest="60"
                        CornerRadius="30"
                        Padding="0"
                        VerticalOptions="Center"
                        BackgroundColor="{StaticResource Primary}"
                        TextColor="White"
                        Command="{Binding CapturePhotoCommand}"
                        IsEnabled="{Binding IsCameraAvailable}"
                        IsVisible="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"/>

                <ActivityIndicator Grid.Column="1"
                                   IsRunning="{Binding IsBusy}"
                                   IsVisible="{Binding IsBusy}"
                                   Color="{StaticResource Primary}"
                                   WidthRequest="40"
                                   HeightRequest="40"/>
            </Grid>
        </Border>

        <!-- Photo Gallery -->
        <RefreshView Grid.Row="1"
                     IsRefreshing="{Binding IsRefreshing}"
                     Command="{Binding RefreshCommand}">

            <CollectionView ItemsSource="{Binding Photos}"
                            BackgroundColor="Transparent">

                <CollectionView.ItemsLayout>
                    <GridItemsLayout Orientation="Vertical"
                                     Span="3"
                                     HorizontalItemSpacing="8"
                                     VerticalItemSpacing="8"/>
                </CollectionView.ItemsLayout>

                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="models:Photo">
                        <Border BackgroundColor="{AppThemeBinding Light=White, Dark={StaticResource Gray800}}"
                                StrokeThickness="1"
                                Stroke="{AppThemeBinding Light={StaticResource Gray200}, Dark={StaticResource Gray700}}"
                                StrokeShape="RoundRectangle 8"
                                Padding="0"
                                Margin="8,4">

                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type pagemodels:PhotoCapturePageModel}}, Path=ViewPhotoCommand}"
                                                      CommandParameter="{Binding .}"/>
                            </Border.GestureRecognizers>

                            <VisualStateManager.VisualStateGroups>
                                <VisualStateGroup Name="CommonStates">
                                    <VisualState Name="Normal" />
                                    <VisualState Name="Pressed">
                                        <VisualState.Setters>
                                            <Setter Property="Opacity" Value="0.7" />
                                        </VisualState.Setters>
                                    </VisualState>
                                </VisualStateGroup>
                            </VisualStateManager.VisualStateGroups>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="150"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="150"/>
                                </Grid.RowDefinitions>

                                <!-- TODO (not showing thumbnail): Thumbnail placeholder -->
                                <Border Grid.Row="0"
                                        BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray700}}"
                                        StrokeShape="RoundRectangle 8,8,0,0">

                                    <Label Text="📷"
                                       FontSize="48"
                                       HorizontalOptions="Center"
                                       VerticalOptions="Center"
                                       TextColor="{AppThemeBinding Light={StaticResource Gray400}, Dark={StaticResource Gray500}}"/>
                                </Border>

                                <!-- Photo info -->
                                <StackLayout Grid.Row="1"
                                             Padding="12,8"
                                             Spacing="4">
                                    <Label Text="{Binding DisplayTitle}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           LineBreakMode="TailTruncation"/>
                                    <Label Text="{Binding CapturedAt, StringFormat='{0:MMM dd, yyyy HH:mm}'}"
                                           FontSize="12"
                                           TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
                                </StackLayout>

                                <!-- Action buttons -->
                                <Grid Grid.Row="2"
                                      Padding="8,0,8,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <Label Grid.Column="0"
                                           Text="{Binding FileSizeDisplay}"
                                           FontSize="10"
                                           VerticalOptions="Center"
                                           TextColor="{AppThemeBinding Light={StaticResource Gray500}, Dark={StaticResource Gray400}}"/>

                                    <Button Grid.Column="1"
                                            Text="{Binding IsFavorite, Converter={StaticResource BoolToFavoriteIconConverter}}"
                                            FontSize="16"
                                            BackgroundColor="Transparent"
                                            TextColor="{Binding IsFavorite, Converter={StaticResource BoolToFavoriteColorConverter}}"
                                            Padding="8,4"
                                            CommandParameter="{Binding .}"
                                            Command="{Binding Path=ToggleFavoriteCommand, Source={RelativeSource AncestorType={x:Type pagemodels:PhotoCapturePageModel}}}" />

                                    <Button Grid.Column="2"
                                            Text="🗑️"
                                            FontSize="14"
                                            BackgroundColor="Transparent"
                                            TextColor="{AppThemeBinding Light={StaticResource Danger}, Dark={StaticResource DangerDark}}"
                                            Padding="8,4"
                                            CommandParameter="{Binding .}"
                                            Command="{Binding Path=DeletePhotoCommand, Source={RelativeSource AncestorType={x:Type pagemodels:PhotoCapturePageModel}}}"/>
                                </Grid>

                                <!-- Favorite indicator -->
                                <Label Grid.Row="0"
                                       Text="⭐"
                                       FontSize="16"
                                       HorizontalOptions="End"
                                       VerticalOptions="Start"
                                       Margin="8"
                                       IsVisible="{Binding IsFavorite}"/>

                                <!-- Debug some properties -->
                                <!--<Label Grid.Row="3"
                                       BindingContext="{Binding .}"
                                       Text="{Binding Path=Width}" />-->

                                <Button Grid.Row="3" Text="Test" Command="{Binding RefreshCommand, Source={RelativeSource AncestorType={x:Type pagemodels:PhotoCapturePageModel}}}" />
                            </Grid>
                        </Border>
                    </DataTemplate>
                </CollectionView.ItemTemplate>

                <CollectionView.EmptyView>
                    <StackLayout Padding="40"
                                 Spacing="16">
                        <Label Text="📷"
                               FontSize="64"
                               HorizontalOptions="Center"
                               TextColor="{AppThemeBinding Light={StaticResource Gray400}, Dark={StaticResource Gray500}}"/>
                        <Label Text="No photos yet"
                               FontSize="18"
                               FontAttributes="Bold"
                               HorizontalOptions="Center"
                               TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
                        <Label Text="Tap the camera button to capture your first photo!"
                               FontSize="14"
                               HorizontalOptions="Center"
                               HorizontalTextAlignment="Center"
                               TextColor="{AppThemeBinding Light={StaticResource Gray500}, Dark={StaticResource Gray400}}"/>
                    </StackLayout>
                </CollectionView.EmptyView>
            </CollectionView>
        </RefreshView>

        <!-- Status bar -->
        <Border Grid.Row="2"
                BackgroundColor="{AppThemeBinding Light={StaticResource Gray50}, Dark={StaticResource Gray900}}"
                Padding="16,8"
                IsVisible="{Binding StatusMessage, Converter={StaticResource StringToBoolConverter}}">
            <Label Text="{Binding StatusMessage}"
                   FontSize="12"
                   HorizontalOptions="Center"
                   TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
        </Border>
    </Grid>
</ContentPage>
