﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:pageModels="clr-namespace:MauiApp10.PageModels"             
             xmlns:models="clr-namespace:MauiApp10.Models"
             xmlns:controls="clr-namespace:MauiApp10.Pages.Controls"
             xmlns:pullToRefresh="clr-namespace:Syncfusion.Maui.Toolkit.PullToRefresh;assembly=Syncfusion.Maui.Toolkit"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             x:Class="MauiApp10.Pages.MainPage"
             x:DataType="pageModels:MainPageModel"
             x:Name="OverviewPage"
             Title="{Binding Today}">

    <ContentPage.Behaviors>
        <toolkit:EventToCommandBehavior
                BindingContext="{Binding Path=BindingContext, Source={x:Reference OverviewPage}, x:DataType=ContentPage}"
                EventName="NavigatedTo"
                Command="{Binding NavigatedToCommand}" />
        <toolkit:EventToCommandBehavior
                BindingContext="{Binding Path=BindingContext, Source={x:Reference OverviewPage}, x:DataType=ContentPage}"
                EventName="NavigatedFrom"
                Command="{Binding NavigatedFromCommand}" />
        <toolkit:EventToCommandBehavior
                BindingContext="{Binding Path=BindingContext, Source={x:Reference OverviewPage}, x:DataType=ContentPage}"
                EventName="Appearing"                
                Command="{Binding AppearingCommand}" />
    </ContentPage.Behaviors>

    <ContentPage.Resources>
        <ResourceDictionary>
            <toolkit:InvertedBoolConverter x:Key="InvertedBoolConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>

    <Grid>
        <pullToRefresh:SfPullToRefresh
            IsRefreshing="{Binding IsRefreshing}"
            RefreshCommand="{Binding RefreshCommand}">
            <pullToRefresh:SfPullToRefresh.PullableContent>
                <ScrollView>
                    <VerticalStackLayout Spacing="{StaticResource LayoutSpacing}" Padding="{StaticResource LayoutPadding}">
                        <controls:CategoryChart />
                        <Label Text="Projects" Style="{StaticResource Title2}"/>
                        <ScrollView Orientation="Horizontal" Margin="-30,0">
                            <HorizontalStackLayout 
                                Spacing="15" Padding="30,0"
                                BindableLayout.ItemsSource="{Binding Projects}">
                                <BindableLayout.ItemTemplate>
                                    <DataTemplate x:DataType="models:Project">
                                        <controls:ProjectCardView WidthRequest="200">
                                            <controls:ProjectCardView.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding NavigateToProjectCommand, Source={RelativeSource AncestorType={x:Type pageModels:MainPageModel}}, x:DataType=pageModels:MainPageModel}" CommandParameter="{Binding .}"/>
                                            </controls:ProjectCardView.GestureRecognizers>
                                        </controls:ProjectCardView>
                                    </DataTemplate>
                                </BindableLayout.ItemTemplate>
                            </HorizontalStackLayout>
                        </ScrollView>
                        <Grid HeightRequest="44">
                            <Label Text="Tasks" Style="{StaticResource Title2}" VerticalOptions="Center"/>
                            <ImageButton 
                                Source="{StaticResource IconClean}"
                                HorizontalOptions="End"
                                VerticalOptions="Center"
                                Aspect="Center"
                                HeightRequest="44"
                                WidthRequest="44"
                                IsVisible="{Binding HasCompletedTasks}"
                                Command="{Binding CleanTasksCommand}"
                                SemanticProperties.Description="Clean tasks" />
                        </Grid>
                        <VerticalStackLayout Spacing="15"
                            BindableLayout.ItemsSource="{Binding Tasks}">
                            <BindableLayout.ItemTemplate>
                                <DataTemplate>
                                    <controls:TaskView TaskCompletedCommand="{Binding TaskCompletedCommand, Source={RelativeSource AncestorType={x:Type pageModels:MainPageModel}}, x:DataType=pageModels:MainPageModel}" />
                                </DataTemplate>
                            </BindableLayout.ItemTemplate>
                        </VerticalStackLayout>
                    </VerticalStackLayout>
                </ScrollView>
            </pullToRefresh:SfPullToRefresh.PullableContent>
        </pullToRefresh:SfPullToRefresh>

        <controls:AddButton 
            IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
            Command="{Binding AddTaskCommand}" />
    </Grid>
</ContentPage>
