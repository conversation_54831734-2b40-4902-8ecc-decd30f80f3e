namespace MauiApp10.Services;

/// <summary>
/// Service for encrypting and decrypting files and data
/// </summary>
public interface IEncryptionService
{
    /// <summary>
    /// Encrypts a file and saves it to the specified path
    /// </summary>
    /// <param name="sourceFilePath">Path to the source file to encrypt</param>
    /// <param name="destinationFilePath">Path where the encrypted file will be saved</param>
    /// <returns>True if encryption was successful, false otherwise</returns>
    Task<bool> EncryptFileAsync(string sourceFilePath, string destinationFilePath);

    /// <summary>
    /// Decrypts a file and saves it to the specified path
    /// </summary>
    /// <param name="encryptedFilePath">Path to the encrypted file</param>
    /// <param name="destinationFilePath">Path where the decrypted file will be saved</param>
    /// <returns>True if decryption was successful, false otherwise</returns>
    Task<bool> DecryptFileAsync(string encryptedFilePath, string destinationFilePath);

    /// <summary>
    /// Encrypts a byte array
    /// </summary>
    /// <param name="data">Data to encrypt</param>
    /// <returns>Encrypted data</returns>
    Task<byte[]> EncryptDataAsync(byte[] data);

    /// <summary>
    /// Decrypts a byte array
    /// </summary>
    /// <param name="encryptedData">Encrypted data to decrypt</param>
    /// <returns>Decrypted data</returns>
    Task<byte[]> DecryptDataAsync(byte[] encryptedData);

    /// <summary>
    /// Generates a secure file name for storing encrypted files
    /// </summary>
    /// <param name="originalFileName">Original file name</param>
    /// <returns>Secure file name</returns>
    string GenerateSecureFileName(string originalFileName);
}
